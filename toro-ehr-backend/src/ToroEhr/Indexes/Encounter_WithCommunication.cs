using Raven.Client.Documents.Indexes;
using ToroEhr.Domain;

namespace ToroEhr.Indexes;

public class Encounter_WithCommunication : AbstractMultiMapIndexCreationTask<Encounter_WithCommunication.Entry>
{
    public class Entry
    {
        public string? EncounterId { get; set; } = null!;
        public string PatientId { get; set; } = null!;
        public string PractitionerId { get; set; } = null!;
        public string LocationId { get; set; } = null!;
        public string EncounterStatus { get; set; } = null!;
        public DateTimeOffset StartAt { get; set; }
        public bool UnseenByPatient { get; set; }
        public bool UnseenByPractitioner { get; set; }
    }
    public Encounter_WithCommunication()
    {
        AddMap<Encounter>(encounters =>
            from encounter in encounters
            select new Entry
            {
                PatientId = encounter.PatientId,
                PractitionerId = encounter.PractitionerId,
                EncounterId = encounter.Id,
                LocationId = encounter.LocationId,
                EncounterStatus = encounter.Status,
                StartAt = encounter.StartAt,
                UnseenByPatient = false,
                UnseenByPractitioner = false,
            });
        AddMap<EncounterEmail>(encounterEmails =>
            from encounterEmail in encounterEmails
            let encounter = LoadDocument<Encounter>(encounterEmail.EncounterId)
            select new Entry
            {
                PatientId = encounterEmail.PatientId,
                PractitionerId = encounter.PractitionerId,
                EncounterId = encounterEmail.EncounterId,
                LocationId = encounter.LocationId,
                EncounterStatus = encounter.Status,
                StartAt = encounter.StartAt,
                UnseenByPatient = !encounterEmail.Sender.SentByPatient ? !encounterEmail.SeenByRecipient : false,
                UnseenByPractitioner = encounterEmail.Sender.SentByPatient ? !encounterEmail.SeenByRecipient : false,
            });
        AddMap<EncounterSms>(encounterSmses =>
            from encounterSms in encounterSmses
            let encounter = LoadDocument<Encounter>(encounterSms.EncounterId)
            select new Entry
            {
                PatientId = encounterSms.PatientId,
                PractitionerId = encounter.PractitionerId,
                EncounterId = encounterSms.EncounterId,
                LocationId = encounter.LocationId,
                EncounterStatus = encounter.Status,
                StartAt = encounter.StartAt,
                UnseenByPatient = !encounterSms.Sender.SentByPatient ? !encounterSms.SeenByRecipient : false,
                UnseenByPractitioner = encounterSms.Sender.SentByPatient ? !encounterSms.SeenByRecipient : false,
            });
        AddMap<EncounterCallRecord>(encounterCalls =>
            from encounterCall in encounterCalls
            let encounter = LoadDocument<Encounter>(encounterCall.EncounterId)
            select new Entry
            {
                PatientId = encounterCall.PatientId,
                PractitionerId = encounter.PractitionerId,
                EncounterId = encounterCall.EncounterId,
                LocationId = encounter.LocationId,
                EncounterStatus = encounter.Status,
                StartAt = encounter.StartAt,
                UnseenByPatient = !encounterCall.Sender.SentByPatient ? !encounterCall.SeenByRecipient : false,
                UnseenByPractitioner = encounterCall.Sender.SentByPatient ? !encounterCall.SeenByRecipient : false,
            });

        Reduce = results => from result in results
                            group result by new
                            {
                                result.PatientId,
                                result.PractitionerId,
                                result.EncounterId,
                                result.LocationId,
                                result.EncounterStatus,
                                result.StartAt,
                            }
            into g
                            select new Entry
                            {
                                PatientId = g.Key.PatientId,
                                PractitionerId = g.Key.PractitionerId,
                                EncounterId = g.Key.EncounterId,
                                LocationId = g.Key.LocationId,
                                EncounterStatus = g.Key.EncounterStatus,
                                StartAt = g.Key.StartAt,
                                UnseenByPatient = g.Any(x => x.UnseenByPatient),
                                UnseenByPractitioner = g.Any(x => x.UnseenByPractitioner),
                            };

        Stores.Add(x => x.PatientId, FieldStorage.Yes);
        Stores.Add(x => x.PractitionerId, FieldStorage.Yes);
        Stores.Add(x => x.EncounterId, FieldStorage.Yes);
        Stores.Add(x => x.LocationId, FieldStorage.Yes);
        Stores.Add(x => x.EncounterStatus, FieldStorage.Yes);
        Stores.Add(x => x.StartAt, FieldStorage.Yes);
        Stores.Add(x => x.UnseenByPatient, FieldStorage.Yes);
        Stores.Add(x => x.UnseenByPractitioner, FieldStorage.Yes);
    }

}
