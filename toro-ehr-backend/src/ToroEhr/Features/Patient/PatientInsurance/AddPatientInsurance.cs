using FluentValidation;
using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Domain;
using ToroEhr.Infrastructure;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;
using ToroEhr.Shared;

namespace ToroEhr.Features.Patient.PatientInsurance;

public sealed record AddPatientInsuranceCommand(
    string Issuer,
    string? GroupId,
    string? MemberId,
    DateOnly Start,
    DateOnly? End,
    string Order,
    string Type,
    decimal? Copay,
    decimal? Deductible,
    string Relation,
    SubscriberRequest? Subscriber,
    IFormFile? CardFront,
    IFormFile? CardBack)
    : AuthRequest<Unit>;

public record SubscriberRequest(string FirstName, string LastName, DateOnly Birthday, string BirthSex);

internal sealed class AddPatientInsuranceAuth : IAuth<SetPatientPersonalInfoCommand, Unit>
{
    public AddPatientInsuranceAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPatient(user);
    }
}

internal sealed class AddPatientInsuranceCommandValidator : AbstractValidator<AddPatientInsuranceCommand>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public AddPatientInsuranceCommandValidator(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;

        RuleFor(x => x.Issuer).NotEmpty();
        RuleFor(x => x.Start).NotEmpty();
        RuleFor(x => x.Order).NotEmpty();
        RuleFor(x => x.Type).NotEmpty();
        RuleFor(x => x.Relation).NotEmpty();

        // validate insurance order uniqueness (primary, secondary, tertiary can only have 1 each)
        RuleFor(x => x.Order)
            .MustAsync(async (order, cancellationToken) => await ValidateInsuranceOrder(order, cancellationToken))
            .WithMessage("Patient already has {PropertyValue} insurance. Only one {PropertyValue} insurance is allowed.")
            .When(x => IsRestrictedOrder(x.Order));

        When(x => x.Relation.Equals("self", StringComparison.OrdinalIgnoreCase) == false, () =>
        {
            RuleFor(x => x.Subscriber!.FirstName).NotEmpty();
            RuleFor(x => x.Subscriber!.LastName).NotEmpty();
            RuleFor(x => x.Subscriber!.Birthday).NotEmpty();
            RuleFor(x => x.Subscriber!.BirthSex).NotEmpty();
        });
    }

    private static bool IsRestrictedOrder(string order)
    {
        return order.Equals("Primary", StringComparison.OrdinalIgnoreCase) ||
               order.Equals("Secondary", StringComparison.OrdinalIgnoreCase) ||
               order.Equals("Tertiary", StringComparison.OrdinalIgnoreCase);
    }

    private async Task<bool> ValidateInsuranceOrder(string order, CancellationToken cancellationToken)
    {
        using var session = _store.OpenAsyncSession();

        var existingInsurances = await session.Query<Domain.PatientInsurance>()
            .Where(x => x.PatientId == _user.PatientId && x.Order == order)
            .ToListAsync(cancellationToken);

        return !existingInsurances.Any();
    }
}

internal sealed class AddPatientInsuranceHandler : IRequestHandler<AddPatientInsuranceCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly S3FileService _fileService;
    private readonly UserRequestSession _user;

    public AddPatientInsuranceHandler(IDocumentStore store, Authenticator authenticator, S3FileService fileService)
    {
        _store = store;
        _fileService = fileService;
        _user = authenticator.User;
    }

    public async Task<Unit> Handle(AddPatientInsuranceCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Subscriber? subscriber = command.Relation != "self" && command.Subscriber != null
            ? new Subscriber(command.Subscriber.FirstName, command.Subscriber.LastName,
                command.Subscriber.Birthday, command.Subscriber.BirthSex)
            : null;

        var folderPath = $"patients/{_user.PatientId!}/documents/{Utils.GenerateRandomId()}";
        var filePathFront = await UploadFileIfExists(command.CardFront, folderPath);
        var filePathBack = await UploadFileIfExists(command.CardBack, folderPath);

        Domain.PatientInsurance insurance = Domain.PatientInsurance.Create(_user.PatientId!, command.Issuer,
            command.GroupId, command.MemberId, command.Start, command.End, command.Order, command.Type, command.Copay,
            command.Deductible, command.Relation, subscriber, filePathFront, filePathBack);

        await session.StoreAsync(insurance, cancellationToken);
        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }

    private async Task<string?> UploadFileIfExists(IFormFile? file, string folderPath)
    {
        if (file == null) return null;

        var filePath = $"{folderPath}/{file.FileName}";
        await using var stream = file.OpenReadStream();
        await _fileService.UploadFile(stream, file.ContentType, Config.S3.AppFilesBucketName, filePath);

        return filePath;
    }
}